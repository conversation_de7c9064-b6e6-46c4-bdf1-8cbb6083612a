apiVersion: v1
kind: ConfigMap
metadata:
  name: prosody-config
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: prosody
data:
  system.lib.lua: |
    -- System library for Jitsi Meet Prosody configuration
    module:set_global();
    
    local function get_room_by_name_and_subdomain(room_name, subdomain)
        local room_address = room_name .. "@" .. subdomain .. "." .. module:get_host()
        return module:get_option("muc_component", "muc.meet.jitsi"):get_room_from_jid(room_address)
    end
    
    return {
        get_room_by_name_and_subdomain = get_room_by_name_and_subdomain
    }
  
  mod_measure_stanza_counts.lua: |
    -- Stanza counting module for monitoring
    module:set_global();
    
    local measure = require "core.statsmanager".measure;
    local stanza_count = measure("amount", "mod_measure_stanza_counts");
    
    local function handle_stanza(event)
        stanza_count();
    end
    
    module:hook("pre-message/full", handle_stanza);
    module:hook("pre-presence/full", handle_stanza);
    module:hook("pre-iq/full", handle_stanza);
    
    module:log("info", "Stanza counting module loaded");
  
  prosody.cfg.lua: |
    -- Prosody Configuration for Jitsi Meet
    
    daemonize = false
    
    plugin_paths = { "/prosody-plugins/", "/prosody-plugins-custom/" }
    
    -- Enable TLS with Let's Encrypt certificates
    ssl = {
        key = "/certs/tls.key";
        certificate = "/certs/tls.crt";
    }
    c2s_require_encryption = false
    s2s_require_encryption = false
    s2s_secure_auth = false
    
    -- Allow plaintext authentication
    allow_unencrypted_plain_auth = true
    
    -- Security configuration
    security_policy = {
        c2s = {
            require_encryption = false;
        };
        s2s = {
            require_encryption = false;
        };
    }
    
    -- Authentication modules
    modules_enabled = {
        "saslauth"; -- Authentication for clients and servers
        "posix"; -- POSIX functionality
        "disk_cache"; -- File-based caching
        "version"; -- Replies to server version requests
        "uptime"; -- Report how long server has been running
        "time"; -- Let others know the time here
        "ping"; -- Replies to XMPP pings with pongs
        "pep"; -- Enables users to publish their mood, activity, playing music and more
        "register"; -- Allow users to register on this server using a client and change passwords
        "admin_adhoc"; -- Allows administration via an XMPP client that supports ad-hoc commands
        "admin_telnet"; -- Opens telnet console interface on localhost port 5582
        "bosh"; -- Enable BOSH clients
        "websocket"; -- Enable WebSocket clients
        "announce"; -- Send announcement to all online users
        "welcome"; -- Welcome users who register accounts
        "watchdog"; -- Alert admins of issues with the server
        "motd"; -- Send a message to users when they log in
        "legacyauth"; -- Legacy authentication
        "proxy65"; -- Enables a file transfer proxy service
    }
    
    -- Allow plaintext authentication
    allow_unencrypted_plain_auth = true
    authentication = "internal_hashed"
    
    VirtualHost "meet.jitsi"
        authentication = "anonymous"
        allow_unencrypted_plain_auth = true
        c2s_require_encryption = false
        s2s_require_encryption = false
        modules_enabled = {
            "bosh";
            "websocket";
            "pubsub";
            "ping";
            "speakerstats";
            "conference_duration";
            "end_conference";
            "muc_lobby_rooms";
            "muc_breakout_rooms";
            "av_moderation";
            "room_metadata";
        }
        
        main_muc = "muc.meet.jitsi"
        lobby_muc = "lobby.meet.jitsi"
        breakout_rooms_muc = "breakout.meet.jitsi"
        
        speakerstats_component = "speakerstats.meet.jitsi"
        conference_duration_component = "conferenceduration.meet.jitsi"
        end_conference_component = "endconference.meet.jitsi"
        av_moderation_component = "avmoderation.meet.jitsi"

        -- HTTP host configuration for websocket
        http_host = "meet.jitsi"

        c2s_require_encryption = false
        
    VirtualHost "auth.meet.jitsi"
        authentication = "internal_hashed"
        allow_unencrypted_plain_auth = true
        c2s_require_encryption = false
        s2s_require_encryption = false
        
        admins = { "<EMAIL>" }
        
    VirtualHost "recorder.meet.jitsi"
        authentication = "internal_hashed"
        
    Component "muc.meet.jitsi" "muc"
        storage = "memory"
        modules_enabled = {
            "muc_meeting_id";
            "muc_domain_mapper";
            "polls";
        }
        
        muc_room_locking = false
        muc_room_default_public_jids = true
        
    Component "lobby.meet.jitsi" "muc"
        storage = "memory"
        restrict_room_creation = true
        muc_room_locking = false
        muc_room_default_public_jids = true
        
    Component "breakout.meet.jitsi" "muc"
        storage = "memory"
        restrict_room_creation = true
        muc_room_locking = false
        muc_room_default_public_jids = true
        
    Component "focus.meet.jitsi" "client_proxy"
        target_address = "jicofo-service:8888"
        
    Component "speakerstats.meet.jitsi" "speakerstats_component"
        muc_component = "muc.meet.jitsi"
        
    Component "conferenceduration.meet.jitsi" "conference_duration_component"
        muc_component = "muc.meet.jitsi"
        
    Component "endconference.meet.jitsi" "end_conference"
        muc_component = "muc.meet.jitsi"
        
    Component "avmoderation.meet.jitsi" "av_moderation_component"
        muc_component = "muc.meet.jitsi"
        
    -- Internal MUC component
    Component "internal-muc.meet.jitsi" "muc"
        storage = "memory"
        modules_enabled = {
            "ping";
        }
        admins = { "<EMAIL>", "<EMAIL>" }
        muc_room_locking = false
        muc_room_default_public_jids = true
        
    -- HTTP/HTTPS ports configuration
    http_ports = { 5280 }
    https_ports = { 5281 }
    http_interfaces = { "0.0.0.0", "::" }
    https_interfaces = { "0.0.0.0", "::" }

    -- Force websocket to use HTTP port
    http_default_host = "*"

    -- WebSocket configuration
    cross_domain_websocket = true
    consider_websocket_secure = true

    -- Configure logging
    log = {
        info = "*console";
    }
